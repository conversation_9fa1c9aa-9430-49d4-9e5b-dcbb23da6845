using System.Diagnostics;
using System.IO;
using PeNet;
using FlagFinder.Models;
using FlagFinder.Utils;

namespace FlagFinder.Services
{
    /// <summary>
    /// Main service for analyzing executable files to extract command-line flags
    /// </summary>
    public class ExecutableAnalyzer
    {
        private readonly StringExtractor _stringExtractor;
        private readonly FlagPatternMatcher _patternMatcher;

        public ExecutableAnalyzer()
        {
            _stringExtractor = new StringExtractor();
            _patternMatcher = new FlagPatternMatcher();
        }

        /// <summary>
        /// Analyze an executable file to extract command-line flags
        /// </summary>
        public async Task<ExecutableInfo> AnalyzeExecutableAsync(string filePath, IProgress<string>? progress = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var executableInfo = new ExecutableInfo
            {
                FilePath = filePath,
                AnalysisStatus = "Starting analysis..."
            };

            try
            {
                progress?.Report("Validating file...");
                await ValidateFileAsync(filePath);

                progress?.Report("Reading file information...");
                await PopulateFileInfoAsync(executableInfo);

                progress?.Report("Extracting strings from executable...");
                executableInfo.AnalysisStatus = "Extracting strings...";
                var strings = await _stringExtractor.ExtractStringsAsync(filePath);

                progress?.Report($"Analyzing {strings.Count} strings for flag patterns...");
                executableInfo.AnalysisStatus = "Analyzing flag patterns...";
                var flags = _patternMatcher.AnalyzeStrings(strings);

                progress?.Report("Finalizing results...");
                executableInfo.AnalysisStatus = "Complete";
                executableInfo.Flags.Clear();
                foreach (var flag in flags)
                {
                    executableInfo.Flags.Add(flag);
                }

                executableInfo.IsAnalyzed = true;
                stopwatch.Stop();
                executableInfo.AnalysisTime = stopwatch.Elapsed;

                progress?.Report($"Analysis complete. Found {flags.Count} potential flags.");
            }
            catch (Exception ex)
            {
                executableInfo.AnalysisStatus = $"Error: {ex.Message}";
                stopwatch.Stop();
                executableInfo.AnalysisTime = stopwatch.Elapsed;
                throw;
            }

            return executableInfo;
        }

        /// <summary>
        /// Validate that the file can be analyzed
        /// </summary>
        private async Task ValidateFileAsync(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"File not found: {filePath}");

            var fileInfo = new FileInfo(filePath);
            
            if (fileInfo.Length == 0)
                throw new InvalidOperationException("File is empty");

            if (fileInfo.Length > Constants.MaxFileSize)
                throw new InvalidOperationException($"File is too large (max {Constants.MaxFileSize / (1024 * 1024)} MB)");

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            if (!Constants.SupportedExtensions.Any(ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase)))
                throw new NotSupportedException($"Unsupported file type: {extension}");

            // Try to verify it's a valid PE file
            try
            {
                var peFile = new PeFile(filePath);
                if (peFile.ImageNtHeaders == null)
                    throw new InvalidOperationException("File is not a valid PE executable");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Unable to read PE file: {ex.Message}");
            }

            await Task.CompletedTask; // Make method async for consistency
        }

        /// <summary>
        /// Populate basic file information
        /// </summary>
        private async Task PopulateFileInfoAsync(ExecutableInfo executableInfo)
        {
            var fileInfo = new FileInfo(executableInfo.FilePath);
            executableInfo.FileSize = fileInfo.Length;
            executableInfo.LastModified = fileInfo.LastWriteTime;

            try
            {
                var peFile = new PeFile(executableInfo.FilePath);
                
                // Get architecture
                if (peFile.ImageNtHeaders != null)
                {
                    executableInfo.Architecture = peFile.ImageNtHeaders.FileHeader.Machine switch
                    {
                        PeNet.Header.Pe.MachineType.I386 => "x86 (32-bit)",
                        PeNet.Header.Pe.MachineType.Amd64 => "x64 (64-bit)",
                        PeNet.Header.Pe.MachineType.Ia64 => "Itanium (64-bit)",
                        PeNet.Header.Pe.MachineType.Arm => "ARM",
                        PeNet.Header.Pe.MachineType.Arm64 => "ARM64",
                        _ => $"Unknown ({peFile.ImageNtHeaders.FileHeader.Machine})"
                    };
                }

                // Try to get version information
                var versionInfo = FileVersionInfo.GetVersionInfo(executableInfo.FilePath);
                if (!string.IsNullOrEmpty(versionInfo.FileVersion))
                    executableInfo.Version = versionInfo.FileVersion;
                if (!string.IsNullOrEmpty(versionInfo.FileDescription))
                    executableInfo.Description = versionInfo.FileDescription;
            }
            catch (Exception ex)
            {
                // Non-critical error, continue with analysis
                Console.WriteLine($"Warning: Could not read PE headers: {ex.Message}");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Check if a file appears to be a valid executable
        /// </summary>
        public static bool IsValidExecutable(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (!Constants.SupportedExtensions.Any(ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase)))
                    return false;

                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0 || fileInfo.Length > Constants.MaxFileSize)
                    return false;

                // Quick PE validation
                var peFile = new PeFile(filePath);
                return peFile.ImageNtHeaders != null;
            }
            catch
            {
                return false;
            }
        }
    }
}
