using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using FlagFinder.Utils;

namespace FlagFinder.Converters
{
    /// <summary>
    /// Converts confidence values to colors for visual indication
    /// </summary>
    public class ConfidenceToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double confidence)
            {
                if (confidence >= Constants.Confidence.High)
                    return new SolidColorBrush(Colors.Green);
                else if (confidence >= Constants.Confidence.Medium)
                    return new SolidColorBrush(Colors.Orange);
                else
                    return new SolidColorBrush(Colors.Red);
            }
            
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
