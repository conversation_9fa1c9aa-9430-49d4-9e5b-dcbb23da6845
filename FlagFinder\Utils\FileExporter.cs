using System.Globalization;
using System.Text;
using System.IO;
using CsvHelper;
using Newtonsoft.Json;
using FlagFinder.Models;

namespace FlagFinder.Utils
{
    /// <summary>
    /// Handles exporting analysis results to various formats
    /// </summary>
    public static class FileExporter
    {
        /// <summary>
        /// Export formats supported by the application
        /// </summary>
        public enum ExportFormat
        {
            Json,
            Csv,
            Text
        }

        /// <summary>
        /// Export executable analysis results to specified format
        /// </summary>
        public static async Task ExportAsync(ExecutableInfo executableInfo, string outputPath, ExportFormat format)
        {
            switch (format)
            {
                case ExportFormat.Json:
                    await ExportToJsonAsync(executableInfo, outputPath);
                    break;
                case ExportFormat.Csv:
                    await ExportToCsvAsync(executableInfo, outputPath);
                    break;
                case ExportFormat.Text:
                    await ExportToTextAsync(executableInfo, outputPath);
                    break;
                default:
                    throw new ArgumentException($"Unsupported export format: {format}");
            }
        }

        /// <summary>
        /// Export to JSON format
        /// </summary>
        private static async Task ExportToJsonAsync(ExecutableInfo executableInfo, string outputPath)
        {
            var exportData = new
            {
                ExecutableInfo = new
                {
                    executableInfo.FileName,
                    executableInfo.FilePath,
                    executableInfo.FileSize,
                    FileSizeFormatted = executableInfo.FileSizeFormatted,
                    executableInfo.LastModified,
                    executableInfo.Architecture,
                    executableInfo.Version,
                    executableInfo.Description,
                    executableInfo.AnalysisTime,
                    FlagCount = executableInfo.FlagCount
                },
                Flags = executableInfo.Flags.Select(f => new
                {
                    f.ShortForm,
                    f.LongForm,
                    f.Description,
                    f.ArgumentType,
                    f.DefaultValue,
                    f.IsRequired,
                    f.TakesValue,
                    f.Usage,
                    f.Confidence,
                    DisplayName = f.DisplayName
                }).ToList(),
                ExportInfo = new
                {
                    ExportedAt = DateTime.Now,
                    ExportedBy = "FlagFinder v1.0",
                    Format = "JSON"
                }
            };

            var json = JsonConvert.SerializeObject(exportData, Formatting.Indented);
            await File.WriteAllTextAsync(outputPath, json, Encoding.UTF8);
        }

        /// <summary>
        /// Export to CSV format
        /// </summary>
        private static async Task ExportToCsvAsync(ExecutableInfo executableInfo, string outputPath)
        {
            using var writer = new StringWriter();
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

            // Write header
            csv.WriteField("Flag Name");
            csv.WriteField("Short Form");
            csv.WriteField("Long Form");
            csv.WriteField("Description");
            csv.WriteField("Argument Type");
            csv.WriteField("Default Value");
            csv.WriteField("Required");
            csv.WriteField("Takes Value");
            csv.WriteField("Usage");
            csv.WriteField("Confidence");
            csv.NextRecord();

            // Write flag data
            foreach (var flag in executableInfo.Flags)
            {
                csv.WriteField(flag.DisplayName);
                csv.WriteField(flag.ShortForm);
                csv.WriteField(flag.LongForm);
                csv.WriteField(flag.Description);
                csv.WriteField(flag.ArgumentType);
                csv.WriteField(flag.DefaultValue);
                csv.WriteField(flag.IsRequired ? "Yes" : "No");
                csv.WriteField(flag.TakesValue ? "Yes" : "No");
                csv.WriteField(flag.Usage);
                csv.WriteField(flag.Confidence.ToString("F2"));
                csv.NextRecord();
            }

            await File.WriteAllTextAsync(outputPath, writer.ToString(), Encoding.UTF8);
        }

        /// <summary>
        /// Export to plain text format
        /// </summary>
        private static async Task ExportToTextAsync(ExecutableInfo executableInfo, string outputPath)
        {
            var sb = new StringBuilder();

            // Header
            sb.AppendLine("FlagFinder Analysis Report");
            sb.AppendLine("========================");
            sb.AppendLine();

            // File information
            sb.AppendLine("File Information:");
            sb.AppendLine($"  Name: {executableInfo.FileName}");
            sb.AppendLine($"  Path: {executableInfo.FilePath}");
            sb.AppendLine($"  Size: {executableInfo.FileSizeFormatted}");
            sb.AppendLine($"  Modified: {executableInfo.LastModified:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"  Architecture: {executableInfo.Architecture}");
            if (!string.IsNullOrEmpty(executableInfo.Version))
                sb.AppendLine($"  Version: {executableInfo.Version}");
            if (!string.IsNullOrEmpty(executableInfo.Description))
                sb.AppendLine($"  Description: {executableInfo.Description}");
            sb.AppendLine($"  Analysis Time: {executableInfo.AnalysisTime.TotalSeconds:F2} seconds");
            sb.AppendLine();

            // Summary
            sb.AppendLine($"Analysis Summary:");
            sb.AppendLine($"  Total Flags Found: {executableInfo.FlagCount}");
            var highConfidenceFlags = executableInfo.Flags.Count(f => f.Confidence >= Constants.Confidence.High);
            var mediumConfidenceFlags = executableInfo.Flags.Count(f => f.Confidence >= Constants.Confidence.Medium && f.Confidence < Constants.Confidence.High);
            var lowConfidenceFlags = executableInfo.Flags.Count(f => f.Confidence < Constants.Confidence.Medium);
            sb.AppendLine($"  High Confidence: {highConfidenceFlags}");
            sb.AppendLine($"  Medium Confidence: {mediumConfidenceFlags}");
            sb.AppendLine($"  Low Confidence: {lowConfidenceFlags}");
            sb.AppendLine();

            // Flags
            sb.AppendLine("Discovered Command-Line Flags:");
            sb.AppendLine("==============================");
            sb.AppendLine();

            foreach (var flag in executableInfo.Flags.OrderByDescending(f => f.Confidence))
            {
                sb.AppendLine($"Flag: {flag.DisplayName}");
                if (!string.IsNullOrEmpty(flag.Description))
                    sb.AppendLine($"  Description: {flag.Description}");
                if (!string.IsNullOrEmpty(flag.ArgumentType))
                    sb.AppendLine($"  Argument Type: {flag.ArgumentType}");
                if (!string.IsNullOrEmpty(flag.DefaultValue))
                    sb.AppendLine($"  Default Value: {flag.DefaultValue}");
                sb.AppendLine($"  Required: {(flag.IsRequired ? "Yes" : "No")}");
                sb.AppendLine($"  Takes Value: {(flag.TakesValue ? "Yes" : "No")}");
                if (!string.IsNullOrEmpty(flag.Usage))
                    sb.AppendLine($"  Usage: {flag.Usage}");
                sb.AppendLine($"  Confidence: {flag.Confidence:F2}");
                sb.AppendLine();
            }

            // Footer
            sb.AppendLine($"Report generated by FlagFinder v1.0 on {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

            await File.WriteAllTextAsync(outputPath, sb.ToString(), Encoding.UTF8);
        }

        /// <summary>
        /// Get the appropriate file extension for the export format
        /// </summary>
        public static string GetFileExtension(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.Json => ".json",
                ExportFormat.Csv => ".csv",
                ExportFormat.Text => ".txt",
                _ => ".txt"
            };
        }

        /// <summary>
        /// Get a suggested filename for export
        /// </summary>
        public static string GetSuggestedFileName(ExecutableInfo executableInfo, ExportFormat format)
        {
            var baseName = Path.GetFileNameWithoutExtension(executableInfo.FileName);
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var extension = GetFileExtension(format);
            
            return $"{baseName}_flags_{timestamp}{extension}";
        }
    }
}
