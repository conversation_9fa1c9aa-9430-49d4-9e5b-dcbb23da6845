using System.Windows;

namespace FlagFinder
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // Handle command line arguments if file is passed
            if (e.Args.Length > 0)
            {
                var filePath = e.Args[0];
                if (System.IO.File.Exists(filePath))
                {
                    // Pass the file path to the main window
                    var mainWindow = new MainWindow(filePath);
                    mainWindow.Show();
                    return;
                }
            }
            
            // Normal startup
            var normalMainWindow = new MainWindow();
            normalMainWindow.Show();
        }
    }
}
