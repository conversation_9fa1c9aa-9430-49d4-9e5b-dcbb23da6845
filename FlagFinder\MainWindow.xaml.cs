using System.Windows;
using System.Windows.Input;
using FlagFinder.ViewModels;
using FlagFinder.Services;

namespace FlagFinder
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly MainViewModel _viewModel;

        public MainWindow()
        {
            InitializeComponent();
            _viewModel = new MainViewModel();
            DataContext = _viewModel;
        }

        public MainWindow(string filePath) : this()
        {
            // Load file passed from command line
            Loaded += async (s, e) => await _viewModel.LoadFileAsync(filePath);
        }

        private void Window_Drop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files?.Length > 0)
                {
                    var filePath = files[0];
                    if (ExecutableAnalyzer.IsValidExecutable(filePath))
                    {
                        _ = _viewModel.LoadFileAsync(filePath);
                    }
                    else
                    {
                        MessageBox.Show("Please drop a valid executable file (.exe or .dll).", 
                                      "Invalid File", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
        }

        private void Window_DragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files?.Length > 0 && ExecutableAnalyzer.IsValidExecutable(files[0]))
                {
                    e.Effects = DragDropEffects.Copy;
                }
                else
                {
                    e.Effects = DragDropEffects.None;
                }
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
            
            e.Handled = true;
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            var aboutMessage = "FlagFinder v1.0\n\n" +
                             "A Windows desktop application for analyzing executable files\n" +
                             "to automatically detect and extract command-line flags and arguments.\n\n" +
                             "Features:\n" +
                             "• Analyze .exe and .dll files\n" +
                             "• Extract command-line flags and descriptions\n" +
                             "• Export results to JSON, CSV, or text format\n" +
                             "• Drag-and-drop support\n\n" +
                             "Developed with WPF and .NET 8";

            MessageBox.Show(aboutMessage, "About FlagFinder", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
