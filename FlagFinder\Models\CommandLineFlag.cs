using System.ComponentModel;

namespace FlagFinder.Models
{
    /// <summary>
    /// Represents a command-line flag or argument discovered in an executable
    /// </summary>
    public class CommandLineFlag : INotifyPropertyChanged
    {
        private string _shortForm = string.Empty;
        private string _longForm = string.Empty;
        private string _description = string.Empty;
        private string _argumentType = string.Empty;
        private string _defaultValue = string.Empty;
        private bool _isRequired;
        private bool _takesValue;
        private string _usage = string.Empty;
        private double _confidence;

        /// <summary>
        /// Short form of the flag (e.g., "-h")
        /// </summary>
        public string ShortForm
        {
            get => _shortForm;
            set
            {
                _shortForm = value;
                OnPropertyChanged(nameof(ShortForm));
            }
        }

        /// <summary>
        /// Long form of the flag (e.g., "--help")
        /// </summary>
        public string LongForm
        {
            get => _longForm;
            set
            {
                _longForm = value;
                OnPropertyChanged(nameof(LongForm));
            }
        }

        /// <summary>
        /// Description or help text for the flag
        /// </summary>
        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged(nameof(Description));
            }
        }

        /// <summary>
        /// Expected argument type (string, int, bool, etc.)
        /// </summary>
        public string ArgumentType
        {
            get => _argumentType;
            set
            {
                _argumentType = value;
                OnPropertyChanged(nameof(ArgumentType));
            }
        }

        /// <summary>
        /// Default value if any
        /// </summary>
        public string DefaultValue
        {
            get => _defaultValue;
            set
            {
                _defaultValue = value;
                OnPropertyChanged(nameof(DefaultValue));
            }
        }

        /// <summary>
        /// Whether this flag is required
        /// </summary>
        public bool IsRequired
        {
            get => _isRequired;
            set
            {
                _isRequired = value;
                OnPropertyChanged(nameof(IsRequired));
            }
        }

        /// <summary>
        /// Whether this flag takes a value/argument
        /// </summary>
        public bool TakesValue
        {
            get => _takesValue;
            set
            {
                _takesValue = value;
                OnPropertyChanged(nameof(TakesValue));
            }
        }

        /// <summary>
        /// Usage example
        /// </summary>
        public string Usage
        {
            get => _usage;
            set
            {
                _usage = value;
                OnPropertyChanged(nameof(Usage));
            }
        }

        /// <summary>
        /// Confidence level of detection (0.0 to 1.0)
        /// </summary>
        public double Confidence
        {
            get => _confidence;
            set
            {
                _confidence = Math.Max(0.0, Math.Min(1.0, value));
                OnPropertyChanged(nameof(Confidence));
            }
        }

        /// <summary>
        /// Display name combining short and long forms
        /// </summary>
        public string DisplayName
        {
            get
            {
                if (!string.IsNullOrEmpty(ShortForm) && !string.IsNullOrEmpty(LongForm))
                    return $"{ShortForm}, {LongForm}";
                return !string.IsNullOrEmpty(LongForm) ? LongForm : ShortForm;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
