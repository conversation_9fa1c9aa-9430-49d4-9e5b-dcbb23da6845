using System.Text.RegularExpressions;

namespace FlagFinder.Utils
{
    /// <summary>
    /// Constants and patterns used throughout the application
    /// </summary>
    public static class Constants
    {
        /// <summary>
        /// Common command-line flag patterns
        /// </summary>
        public static class FlagPatterns
        {
            // Short flags: -h, -v, -f, etc.
            public static readonly Regex ShortFlag = new(@"-[a-zA-Z](?![a-zA-Z])", RegexOptions.Compiled);
            
            // Long flags: --help, --version, --file, etc.
            public static readonly Regex LongFlag = new(@"--[a-zA-Z][a-zA-Z0-9-_]*", RegexOptions.Compiled);
            
            // Combined patterns: -h, --help
            public static readonly Regex CombinedFlag = new(@"(-[a-zA-Z])\s*,?\s*(--[a-zA-Z][a-zA-Z0-9-_]*)", RegexOptions.Compiled);
            
            // Help text patterns
            public static readonly Regex HelpDescription = new(@"(-[a-zA-Z]|--[a-zA-Z][a-zA-Z0-9-_]*)\s+(.{10,100})", RegexOptions.Compiled);
            
            // Usage patterns
            public static readonly Regex UsagePattern = new(@"Usage:\s*(.+)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
            
            // Option with argument: --file <filename>, -f FILE
            public static readonly Regex FlagWithArg = new(@"(--?[a-zA-Z][a-zA-Z0-9-_]*)\s+[<\[]?([A-Z_]+|[a-z_]+)[>\]]?", RegexOptions.Compiled);
        }

        /// <summary>
        /// Common help-related strings to look for
        /// </summary>
        public static readonly string[] HelpStrings = {
            "help", "usage", "options", "arguments", "parameters", "flags",
            "commands", "syntax", "examples", "version", "verbose", "quiet",
            "debug", "output", "input", "file", "directory", "config"
        };

        /// <summary>
        /// Common argument types
        /// </summary>
        public static readonly Dictionary<string, string> ArgumentTypes = new()
        {
            { "FILE", "string" },
            { "FILENAME", "string" },
            { "PATH", "string" },
            { "DIR", "string" },
            { "DIRECTORY", "string" },
            { "URL", "string" },
            { "STRING", "string" },
            { "STR", "string" },
            { "TEXT", "string" },
            { "NUM", "integer" },
            { "NUMBER", "integer" },
            { "INT", "integer" },
            { "INTEGER", "integer" },
            { "COUNT", "integer" },
            { "SIZE", "integer" },
            { "PORT", "integer" },
            { "BOOL", "boolean" },
            { "BOOLEAN", "boolean" },
            { "FLAG", "boolean" },
            { "FLOAT", "float" },
            { "DOUBLE", "float" },
            { "DECIMAL", "float" }
        };

        /// <summary>
        /// Common flag descriptions that indicate boolean flags
        /// </summary>
        public static readonly string[] BooleanIndicators = {
            "enable", "disable", "turn on", "turn off", "activate", "deactivate",
            "show", "hide", "verbose", "quiet", "debug", "force", "recursive",
            "interactive", "yes", "no", "true", "false"
        };

        /// <summary>
        /// Confidence thresholds
        /// </summary>
        public static class Confidence
        {
            public const double High = 0.8;
            public const double Medium = 0.6;
            public const double Low = 0.4;
            public const double VeryLow = 0.2;
        }

        /// <summary>
        /// File extensions to analyze
        /// </summary>
        public static readonly string[] SupportedExtensions = { ".exe", ".dll" };

        /// <summary>
        /// Maximum file size to analyze (100 MB)
        /// </summary>
        public const long MaxFileSize = 100 * 1024 * 1024;

        /// <summary>
        /// Maximum number of strings to extract from executable
        /// </summary>
        public const int MaxStringsToExtract = 10000;
    }
}
