{"version": 3, "targets": {"net8.0-windows7.0": {"CsvHelper/30.0.1": {"type": "package", "compile": {"lib/net6.0/CsvHelper.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/CsvHelper.dll": {"related": ".xml"}}}, "Microsoft.Toolkit.Mvvm/7.1.2": {"type": "package", "compile": {"lib/net5.0/Microsoft.Toolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Microsoft.Toolkit.Mvvm.dll": {"related": ".pdb;.xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "PeNet/4.0.0": {"type": "package", "dependencies": {"PeNet.Asn1": "2.0.1", "System.Security.Cryptography.Pkcs": "7.0.3"}, "compile": {"lib/net7.0/PeNet.dll": {}}, "runtime": {"lib/net7.0/PeNet.dll": {}}}, "PeNet.Asn1/2.0.1": {"type": "package", "compile": {"lib/net7.0/PeNet.Asn1.dll": {}}, "runtime": {"lib/net7.0/PeNet.Asn1.dll": {}}}, "System.Formats.Asn1/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Cryptography.Pkcs/7.0.3": {"type": "package", "dependencies": {"System.Formats.Asn1": "7.0.0"}, "compile": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"CsvHelper/30.0.1": {"sha512": "rcZtgbWR+As4G3Vpgx0AMNmShGuQLFjkHAPIIflzrfkJCx8/AOd4m96ZRmiU1Wi39qS5UVjV0P8qdgqOo5Cwyg==", "type": "package", "path": "csvhelper/30.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "csvhelper.30.0.1.nupkg.sha512", "csvhelper.nuspec", "lib/net45/CsvHelper.dll", "lib/net45/CsvHelper.xml", "lib/net47/CsvHelper.dll", "lib/net47/CsvHelper.xml", "lib/net5.0/CsvHelper.dll", "lib/net5.0/CsvHelper.xml", "lib/net6.0/CsvHelper.dll", "lib/net6.0/CsvHelper.xml", "lib/netstandard2.0/CsvHelper.dll", "lib/netstandard2.0/CsvHelper.xml", "lib/netstandard2.1/CsvHelper.dll", "lib/netstandard2.1/CsvHelper.xml"]}, "Microsoft.Toolkit.Mvvm/7.1.2": {"sha512": "tUQn6ar9oP7kmYaZ6pWabxpQaqw95Bsf+GG7prEL0y5MV5F60z76lI/Ppps9zcTqwd7e5CrdZBcdfor9WuvohA==", "type": "package", "path": "microsoft.toolkit.mvvm/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/cs/Microsoft.Toolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/cs/Microsoft.Toolkit.Mvvm.SourceGenerators.pdb", "analyzers/dotnet/cs/Microsoft.Toolkit.Mvvm.SourceGenerators.xml", "lib/net5.0/Microsoft.Toolkit.Mvvm.dll", "lib/net5.0/Microsoft.Toolkit.Mvvm.pdb", "lib/net5.0/Microsoft.Toolkit.Mvvm.xml", "lib/netstandard2.0/Microsoft.Toolkit.Mvvm.dll", "lib/netstandard2.0/Microsoft.Toolkit.Mvvm.pdb", "lib/netstandard2.0/Microsoft.Toolkit.Mvvm.xml", "lib/netstandard2.1/Microsoft.Toolkit.Mvvm.dll", "lib/netstandard2.1/Microsoft.Toolkit.Mvvm.pdb", "lib/netstandard2.1/Microsoft.Toolkit.Mvvm.xml", "microsoft.toolkit.mvvm.7.1.2.nupkg.sha512", "microsoft.toolkit.mvvm.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "PeNet/4.0.0": {"sha512": "cYDfj0EkjbG6c008su2ib9evl4//QQTXknCHSAtIQd27sbw6+0KHkLlVeuMltw8Daruy5e5uvMaXT67lEG3ZoQ==", "type": "package", "path": "penet/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net48/PeNet.dll", "lib/net6.0/PeNet.dll", "lib/net7.0/PeNet.dll", "lib/netstandard2.0/PeNet.dll", "lib/netstandard2.1/PeNet.dll", "penet.4.0.0.nupkg.sha512", "penet.nuspec"]}, "PeNet.Asn1/2.0.1": {"sha512": "YR2O2YokSAYB+7CXkCDN3bd6/p0K3/AicCPkOJHKUz500v1D/hulCuVlggguqNc3M0LgSfOZKGvVYg2ud1GA9A==", "type": "package", "path": "penet.asn1/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/PeNet.Asn1.dll", "lib/net461/PeNet.Asn1.dll", "lib/net48/PeNet.Asn1.dll", "lib/net6.0/PeNet.Asn1.dll", "lib/net7.0/PeNet.Asn1.dll", "lib/netstandard1.3/PeNet.Asn1.dll", "lib/netstandard2.0/PeNet.Asn1.dll", "lib/netstandard2.1/PeNet.Asn1.dll", "penet.asn1.2.0.1.nupkg.sha512", "penet.asn1.nuspec"]}, "System.Formats.Asn1/7.0.0": {"sha512": "+nfpV0afLmvJW8+pLlHxRjz3oZJw4fkyU9MMEaMhCsHi/SN9bGF9q79ROubDiwTiCHezmK0uCWkPP7tGFP/4yg==", "type": "package", "path": "system.formats.asn1/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Asn1.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "lib/net462/System.Formats.Asn1.dll", "lib/net462/System.Formats.Asn1.xml", "lib/net6.0/System.Formats.Asn1.dll", "lib/net6.0/System.Formats.Asn1.xml", "lib/net7.0/System.Formats.Asn1.dll", "lib/net7.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.7.0.0.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Pkcs/7.0.3": {"sha512": "yhwEHH5Gzl/VoADrXtt5XC95OFoSjNSWLHNutE7GwdOgefZVRvEXRSooSpL8HHm3qmdd9epqzsWg28UJemt22w==", "type": "package", "path": "system.security.cryptography.pkcs/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.7.0.3.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["CsvHelper >= 30.0.1", "Microsoft.Toolkit.Mvvm >= 7.1.2", "Newtonsoft.Json >= 13.0.3", "PeNet >= 4.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\flagfinder\\FlagFinder\\FlagFinder.csproj", "projectName": "FlagFinder", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\flagfinder\\FlagFinder\\FlagFinder.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\flagfinder\\FlagFinder\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CsvHelper": {"target": "Package", "version": "[30.0.1, )"}, "Microsoft.Toolkit.Mvvm": {"target": "Package", "version": "[7.1.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "PeNet": {"target": "Package", "version": "[4.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}