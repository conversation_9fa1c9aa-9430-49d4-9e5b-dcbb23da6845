<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>

    <AssemblyTitle>FlagFinder</AssemblyTitle>
    <AssemblyDescription>Analyze executable files to extract command-line flags and arguments</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>Copyright © 2025</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="PeNet" Version="4.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="CsvHelper" Version="30.0.1" />
    <PackageReference Include="Microsoft.Toolkit.Mvvm" Version="7.1.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
    <Folder Include="Utils\" />
    <Folder Include="Converters\" />
  </ItemGroup>

</Project>
