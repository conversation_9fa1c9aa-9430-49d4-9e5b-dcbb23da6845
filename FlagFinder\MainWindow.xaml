<Window x:Class="FlagFinder.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:utils="clr-namespace:FlagFinder.Utils"
        mc:Ignorable="d"
        Title="FlagFinder - Command Line Flag Analyzer" 
        Height="700" Width="1200"
        MinHeight="500" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        AllowDrop="True"
        Drop="Window_Drop"
        DragOver="Window_DragOver">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- Menu Bar -->
        <Menu Grid.Row="0">
            <MenuItem Header="_File">
                <MenuItem Header="_Open Executable..." Command="{Binding SelectFileCommand}" />
                <Separator />
                <MenuItem Header="Export as _JSON..." Command="{Binding ExportCommand}" 
                         CommandParameter="{x:Static utils:FileExporter+ExportFormat.Json}" />
                <MenuItem Header="Export as _CSV..." Command="{Binding ExportCommand}" 
                         CommandParameter="{x:Static utils:FileExporter+ExportFormat.Csv}" />
                <MenuItem Header="Export as _Text..." Command="{Binding ExportCommand}" 
                         CommandParameter="{x:Static utils:FileExporter+ExportFormat.Text}" />
                <Separator />
                <MenuItem Header="E_xit" Click="Exit_Click" />
            </MenuItem>
            <MenuItem Header="_Tools">
                <MenuItem Header="_Analyze Current File" Command="{Binding AnalyzeCommand}" />
                <MenuItem Header="_Clear Results" Command="{Binding ClearCommand}" />
            </MenuItem>
            <MenuItem Header="_Help">
                <MenuItem Header="_About" Click="About_Click" />
            </MenuItem>
        </Menu>
        
        <!-- Toolbar -->
        <ToolBar Grid.Row="1" ToolBarTray.IsLocked="True">
            <Button Content="Open File" Command="{Binding SelectFileCommand}" Style="{StaticResource ButtonStyle}" />
            <Separator />
            <Button Content="Analyze" Command="{Binding AnalyzeCommand}" Style="{StaticResource ButtonStyle}" />
            <Button Content="Clear" Command="{Binding ClearCommand}" Style="{StaticResource SecondaryButtonStyle}" />
            <Separator />
            <TextBlock Text="Search:" VerticalAlignment="Center" Margin="10,0,5,0" />
            <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" 
                     Width="200" VerticalAlignment="Center" />
        </ToolBar>
        
        <!-- Main Content -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            
            <!-- File Information Panel -->
            <Border Grid.Column="0" BorderBrush="#FFD0D0D0" BorderThickness="1" Background="White" Padding="10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <TextBlock Text="File Information" Style="{StaticResource HeaderTextStyle}" />
                        
                        <StackPanel Visibility="{Binding HasExecutable, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="Name:" FontWeight="Bold" Style="{StaticResource InfoTextStyle}" />
                            <TextBlock Text="{Binding CurrentExecutable.FileName}" Style="{StaticResource InfoTextStyle}" 
                                      TextWrapping="Wrap" Margin="0,0,0,10" />
                            
                            <TextBlock Text="Size:" FontWeight="Bold" Style="{StaticResource InfoTextStyle}" />
                            <TextBlock Text="{Binding CurrentExecutable.FileSizeFormatted}" Style="{StaticResource InfoTextStyle}" 
                                      Margin="0,0,0,10" />
                            
                            <TextBlock Text="Modified:" FontWeight="Bold" Style="{StaticResource InfoTextStyle}" />
                            <TextBlock Text="{Binding CurrentExecutable.LastModified, StringFormat=yyyy-MM-dd HH:mm:ss}" 
                                      Style="{StaticResource InfoTextStyle}" Margin="0,0,0,10" />
                            
                            <TextBlock Text="Architecture:" FontWeight="Bold" Style="{StaticResource InfoTextStyle}" />
                            <TextBlock Text="{Binding CurrentExecutable.Architecture}" Style="{StaticResource InfoTextStyle}" 
                                      Margin="0,0,0,10" />
                            
                            <TextBlock Text="Version:" FontWeight="Bold" Style="{StaticResource InfoTextStyle}" 
                                      Visibility="{Binding CurrentExecutable.Version, Converter={StaticResource StringToVisibilityConverter}}" />
                            <TextBlock Text="{Binding CurrentExecutable.Version}" Style="{StaticResource InfoTextStyle}" 
                                      Margin="0,0,0,10"
                                      Visibility="{Binding CurrentExecutable.Version, Converter={StaticResource StringToVisibilityConverter}}" />
                            
                            <TextBlock Text="Description:" FontWeight="Bold" Style="{StaticResource InfoTextStyle}" 
                                      Visibility="{Binding CurrentExecutable.Description, Converter={StaticResource StringToVisibilityConverter}}" />
                            <TextBlock Text="{Binding CurrentExecutable.Description}" Style="{StaticResource InfoTextStyle}" 
                                      TextWrapping="Wrap" Margin="0,0,0,10"
                                      Visibility="{Binding CurrentExecutable.Description, Converter={StaticResource StringToVisibilityConverter}}" />
                        </StackPanel>
                        
                        <StackPanel Visibility="{Binding HasResults, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="Analysis Results" Style="{StaticResource HeaderTextStyle}" />
                            
                            <TextBlock Text="Flags Found:" FontWeight="Bold" Style="{StaticResource InfoTextStyle}" />
                            <TextBlock Text="{Binding CurrentExecutable.FlagCount}" Style="{StaticResource InfoTextStyle}" 
                                      Margin="0,0,0,10" />
                            
                            <TextBlock Text="Analysis Time:" FontWeight="Bold" Style="{StaticResource InfoTextStyle}" />
                            <TextBlock Text="{Binding CurrentExecutable.AnalysisTime, StringFormat={}{0:F2} seconds}" 
                                      Style="{StaticResource InfoTextStyle}" Margin="0,0,0,10" />
                        </StackPanel>
                        
                        <TextBlock Text="Drag and drop an executable file here to analyze it" 
                                  Style="{StaticResource InfoTextStyle}" 
                                  TextWrapping="Wrap" 
                                  Foreground="Gray"
                                  Visibility="{Binding HasExecutable, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />
                    </StackPanel>
                </ScrollViewer>
            </Border>
            
            <!-- Splitter -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="#FFD0D0D0" />
            
            <!-- Results Panel -->
            <Border Grid.Column="2" BorderBrush="#FFD0D0D0" BorderThickness="1" Background="White">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    
                    <!-- Progress Bar -->
                    <ProgressBar Grid.Row="0" IsIndeterminate="{Binding IsAnalyzing}" 
                                Style="{StaticResource ModernProgressBar}"
                                Visibility="{Binding IsAnalyzing, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    
                    <!-- Results DataGrid -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding FilteredFlags}" 
                             Style="{StaticResource DataGridStyle}"
                             Visibility="{Binding HasResults, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Flag" Binding="{Binding DisplayName}" Width="150" />
                            <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*" />
                            <DataGridTextColumn Header="Type" Binding="{Binding ArgumentType}" Width="80" />
                            <DataGridTextColumn Header="Required" Binding="{Binding IsRequired, Converter={StaticResource BooleanToYesNoConverter}}" Width="70" />
                            <DataGridTextColumn Header="Takes Value" Binding="{Binding TakesValue, Converter={StaticResource BooleanToYesNoConverter}}" Width="90" />
                            <DataGridTextColumn Header="Default" Binding="{Binding DefaultValue}" Width="100" />
                            <DataGridTemplateColumn Header="Confidence" Width="90">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="{Binding Confidence, Converter={StaticResource ConfidenceToColorConverter}}" 
                                               CornerRadius="3" Padding="5,2">
                                            <TextBlock Text="{Binding Confidence, StringFormat=F2}" 
                                                      Foreground="White" 
                                                      HorizontalAlignment="Center" />
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                    
                    <!-- No Results Message -->
                    <TextBlock Grid.Row="1" Text="No analysis results available. Select and analyze an executable file to see command-line flags."
                              HorizontalAlignment="Center" VerticalAlignment="Center"
                              Foreground="Gray" FontSize="14"
                              Visibility="{Binding HasResults, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />
                </Grid>
            </Border>
        </Grid>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="3" Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
