using System.Text.RegularExpressions;
using FlagFinder.Models;
using FlagFinder.Utils;

namespace FlagFinder.Services
{
    /// <summary>
    /// Analyzes strings to identify command-line flags and their properties
    /// </summary>
    public class FlagPatternMatcher
    {
        /// <summary>
        /// Analyze a collection of strings to find command-line flags
        /// </summary>
        public List<CommandLineFlag> AnalyzeStrings(IEnumerable<string> strings)
        {
            var flags = new Dictionary<string, CommandLineFlag>();
            var stringList = strings.ToList();

            // First pass: Find obvious flag patterns
            foreach (var str in stringList)
            {
                var foundFlags = ExtractFlagsFromString(str);
                foreach (var flag in foundFlags)
                {
                    var key = GetFlagKey(flag);
                    if (!flags.ContainsKey(key))
                    {
                        flags[key] = flag;
                    }
                    else
                    {
                        // Merge information
                        MergeFlags(flags[key], flag);
                    }
                }
            }

            // Second pass: Look for descriptions and usage information
            foreach (var str in stringList)
            {
                UpdateFlagDescriptions(flags.Values, str);
            }

            // Third pass: Infer argument types and requirements
            foreach (var flag in flags.Values)
            {
                InferFlagProperties(flag, stringList);
            }

            return flags.Values.OrderByDescending(f => f.Confidence).ToList();
        }

        /// <summary>
        /// Extract flags from a single string
        /// </summary>
        private List<CommandLineFlag> ExtractFlagsFromString(string input)
        {
            var flags = new List<CommandLineFlag>();

            // Look for combined patterns first (e.g., "-h, --help")
            var combinedMatches = Constants.FlagPatterns.CombinedFlag.Matches(input);
            foreach (Match match in combinedMatches)
            {
                var flag = new CommandLineFlag
                {
                    ShortForm = match.Groups[1].Value,
                    LongForm = match.Groups[2].Value,
                    Confidence = Constants.Confidence.High
                };
                flags.Add(flag);
            }

            // Look for flags with arguments (e.g., "--file <filename>")
            var flagWithArgMatches = Constants.FlagPatterns.FlagWithArg.Matches(input);
            foreach (Match match in flagWithArgMatches)
            {
                var flagName = match.Groups[1].Value;
                var argType = match.Groups[2].Value;
                
                var flag = new CommandLineFlag
                {
                    LongForm = flagName.StartsWith("--") ? flagName : "",
                    ShortForm = flagName.StartsWith("-") && !flagName.StartsWith("--") ? flagName : "",
                    TakesValue = true,
                    ArgumentType = InferArgumentType(argType),
                    Confidence = Constants.Confidence.High
                };
                
                if (string.IsNullOrEmpty(flag.LongForm) && string.IsNullOrEmpty(flag.ShortForm))
                {
                    if (flagName.StartsWith("--"))
                        flag.LongForm = flagName;
                    else
                        flag.ShortForm = flagName;
                }
                
                flags.Add(flag);
            }

            // Look for standalone long flags
            var longFlagMatches = Constants.FlagPatterns.LongFlag.Matches(input);
            foreach (Match match in longFlagMatches)
            {
                var flagName = match.Value;
                if (!flags.Any(f => f.LongForm == flagName))
                {
                    var flag = new CommandLineFlag
                    {
                        LongForm = flagName,
                        Confidence = Constants.Confidence.Medium
                    };
                    flags.Add(flag);
                }
            }

            // Look for standalone short flags
            var shortFlagMatches = Constants.FlagPatterns.ShortFlag.Matches(input);
            foreach (Match match in shortFlagMatches)
            {
                var flagName = match.Value;
                if (!flags.Any(f => f.ShortForm == flagName))
                {
                    var flag = new CommandLineFlag
                    {
                        ShortForm = flagName,
                        Confidence = Constants.Confidence.Medium
                    };
                    flags.Add(flag);
                }
            }

            return flags;
        }

        /// <summary>
        /// Update flag descriptions based on help text patterns
        /// </summary>
        private void UpdateFlagDescriptions(IEnumerable<CommandLineFlag> flags, string input)
        {
            var helpMatches = Constants.FlagPatterns.HelpDescription.Matches(input);
            foreach (Match match in helpMatches)
            {
                var flagName = match.Groups[1].Value;
                var description = match.Groups[2].Value.Trim();
                
                var flag = flags.FirstOrDefault(f => f.ShortForm == flagName || f.LongForm == flagName);
                if (flag != null && string.IsNullOrEmpty(flag.Description))
                {
                    flag.Description = CleanDescription(description);
                    flag.Confidence = Math.Max(flag.Confidence, Constants.Confidence.High);
                }
            }

            // Look for usage patterns
            var usageMatches = Constants.FlagPatterns.UsagePattern.Matches(input);
            foreach (Match match in usageMatches)
            {
                var usage = match.Groups[1].Value.Trim();
                foreach (var flag in flags)
                {
                    if (usage.Contains(flag.ShortForm) || usage.Contains(flag.LongForm))
                    {
                        if (string.IsNullOrEmpty(flag.Usage))
                        {
                            flag.Usage = usage;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Infer additional properties of flags
        /// </summary>
        private void InferFlagProperties(CommandLineFlag flag, List<string> allStrings)
        {
            var flagName = !string.IsNullOrEmpty(flag.LongForm) ? flag.LongForm : flag.ShortForm;
            
            // Infer if it's a boolean flag
            if (!flag.TakesValue)
            {
                var description = flag.Description.ToLowerInvariant();
                if (Constants.BooleanIndicators.Any(indicator => description.Contains(indicator)))
                {
                    flag.ArgumentType = "boolean";
                    flag.TakesValue = false;
                }
            }

            // Look for default values in surrounding text
            foreach (var str in allStrings)
            {
                if (str.Contains(flagName) && str.ToLowerInvariant().Contains("default"))
                {
                    var defaultMatch = Regex.Match(str, @"default[:\s]+([^\s,\)]+)", RegexOptions.IgnoreCase);
                    if (defaultMatch.Success && string.IsNullOrEmpty(flag.DefaultValue))
                    {
                        flag.DefaultValue = defaultMatch.Groups[1].Value;
                    }
                }
            }

            // Infer if required
            foreach (var str in allStrings)
            {
                if (str.Contains(flagName) && 
                    (str.ToLowerInvariant().Contains("required") || str.ToLowerInvariant().Contains("mandatory")))
                {
                    flag.IsRequired = true;
                    break;
                }
            }

            // Boost confidence for well-known flags
            var knownFlags = new[] { "--help", "-h", "--version", "-v", "--verbose", "--quiet", "-q" };
            if (knownFlags.Contains(flag.ShortForm) || knownFlags.Contains(flag.LongForm))
            {
                flag.Confidence = Constants.Confidence.High;
            }
        }

        /// <summary>
        /// Merge information from two flag instances
        /// </summary>
        private void MergeFlags(CommandLineFlag target, CommandLineFlag source)
        {
            if (string.IsNullOrEmpty(target.ShortForm) && !string.IsNullOrEmpty(source.ShortForm))
                target.ShortForm = source.ShortForm;
                
            if (string.IsNullOrEmpty(target.LongForm) && !string.IsNullOrEmpty(source.LongForm))
                target.LongForm = source.LongForm;
                
            if (string.IsNullOrEmpty(target.Description) && !string.IsNullOrEmpty(source.Description))
                target.Description = source.Description;
                
            if (string.IsNullOrEmpty(target.ArgumentType) && !string.IsNullOrEmpty(source.ArgumentType))
                target.ArgumentType = source.ArgumentType;
                
            if (string.IsNullOrEmpty(target.DefaultValue) && !string.IsNullOrEmpty(source.DefaultValue))
                target.DefaultValue = source.DefaultValue;
                
            if (!target.TakesValue && source.TakesValue)
                target.TakesValue = source.TakesValue;
                
            if (!target.IsRequired && source.IsRequired)
                target.IsRequired = source.IsRequired;
                
            target.Confidence = Math.Max(target.Confidence, source.Confidence);
        }

        /// <summary>
        /// Generate a unique key for a flag
        /// </summary>
        private string GetFlagKey(CommandLineFlag flag)
        {
            return !string.IsNullOrEmpty(flag.LongForm) ? flag.LongForm : flag.ShortForm;
        }

        /// <summary>
        /// Infer argument type from string
        /// </summary>
        private string InferArgumentType(string argString)
        {
            var upper = argString.ToUpperInvariant();
            return Constants.ArgumentTypes.TryGetValue(upper, out var type) ? type : "string";
        }

        /// <summary>
        /// Clean up description text
        /// </summary>
        private string CleanDescription(string description)
        {
            // Remove extra whitespace and common prefixes
            description = Regex.Replace(description, @"\s+", " ").Trim();
            description = Regex.Replace(description, @"^[-\s]*", "");
            
            // Capitalize first letter
            if (description.Length > 0)
            {
                description = char.ToUpperInvariant(description[0]) + description.Substring(1);
            }
            
            return description;
        }
    }
}
