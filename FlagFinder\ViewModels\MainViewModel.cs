using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Windows.Input;
using Microsoft.Toolkit.Mvvm.ComponentModel;
using Microsoft.Toolkit.Mvvm.Input;
using Microsoft.Win32;
using FlagFinder.Models;
using FlagFinder.Services;
using FlagFinder.Utils;

namespace FlagFinder.ViewModels
{
    /// <summary>
    /// Main view model for the application
    /// </summary>
    public partial class MainViewModel : ObservableObject
    {
        private readonly ExecutableAnalyzer _analyzer;
        private ExecutableInfo? _currentExecutable;
        private bool _isAnalyzing;
        private string _statusMessage = "Ready";
        private string _searchText = string.Empty;
        private ObservableCollection<CommandLineFlag> _filteredFlags = new();

        public MainViewModel()
        {
            _analyzer = new ExecutableAnalyzer();
            
            // Initialize commands
            SelectFileCommand = new AsyncRelayCommand(SelectFileAsync);
            AnalyzeCommand = new AsyncRelayCommand(AnalyzeCurrentFileAsync, () => CurrentExecutable != null && !IsAnalyzing);
            ExportCommand = new AsyncRelayCommand<FileExporter.ExportFormat?>(ExportResultsAsync, 
                format => CurrentExecutable?.IsAnalyzed == true);
            ClearCommand = new RelayCommand(ClearResults);
        }

        #region Properties

        /// <summary>
        /// Currently loaded executable information
        /// </summary>
        public ExecutableInfo? CurrentExecutable
        {
            get => _currentExecutable;
            set
            {
                SetProperty(ref _currentExecutable, value);
                OnPropertyChanged(nameof(HasExecutable));
                OnPropertyChanged(nameof(HasResults));
                AnalyzeCommand.NotifyCanExecuteChanged();
                ExportCommand.NotifyCanExecuteChanged();
                UpdateFilteredFlags();
            }
        }

        /// <summary>
        /// Whether an executable is currently loaded
        /// </summary>
        public bool HasExecutable => CurrentExecutable != null;

        /// <summary>
        /// Whether analysis results are available
        /// </summary>
        public bool HasResults => CurrentExecutable?.IsAnalyzed == true;

        /// <summary>
        /// Whether analysis is currently in progress
        /// </summary>
        public bool IsAnalyzing
        {
            get => _isAnalyzing;
            set
            {
                SetProperty(ref _isAnalyzing, value);
                AnalyzeCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// Current status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Search/filter text for flags
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                UpdateFilteredFlags();
            }
        }

        /// <summary>
        /// Filtered collection of flags based on search text
        /// </summary>
        public ObservableCollection<CommandLineFlag> FilteredFlags
        {
            get => _filteredFlags;
            set => SetProperty(ref _filteredFlags, value);
        }

        #endregion

        #region Commands

        public IAsyncRelayCommand SelectFileCommand { get; }
        public IAsyncRelayCommand AnalyzeCommand { get; }
        public IAsyncRelayCommand<FileExporter.ExportFormat?> ExportCommand { get; }
        public IRelayCommand ClearCommand { get; }

        #endregion

        #region Methods

        /// <summary>
        /// Select an executable file for analysis
        /// </summary>
        private async Task SelectFileAsync()
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Select Executable File",
                Filter = "Executable Files (*.exe)|*.exe|Dynamic Link Libraries (*.dll)|*.dll|All Files (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                await LoadFileAsync(openFileDialog.FileName);
            }
        }

        /// <summary>
        /// Load a file for analysis
        /// </summary>
        public Task LoadFileAsync(string filePath)
        {
            try
            {
                StatusMessage = "Loading file...";
                
                if (!ExecutableAnalyzer.IsValidExecutable(filePath))
                {
                    StatusMessage = "Invalid or unsupported file format";
                    return Task.CompletedTask;
                }

                var executableInfo = new ExecutableInfo { FilePath = filePath };
                
                // Get basic file info
                var fileInfo = new FileInfo(filePath);
                executableInfo.FileSize = fileInfo.Length;
                executableInfo.LastModified = fileInfo.LastWriteTime;

                CurrentExecutable = executableInfo;
                StatusMessage = $"Loaded: {executableInfo.FileName}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading file: {ex.Message}";
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Analyze the currently loaded executable
        /// </summary>
        private async Task AnalyzeCurrentFileAsync()
        {
            if (CurrentExecutable == null) return;

            try
            {
                IsAnalyzing = true;
                StatusMessage = "Starting analysis...";

                var progress = new Progress<string>(message => StatusMessage = message);
                
                var result = await _analyzer.AnalyzeExecutableAsync(CurrentExecutable.FilePath, progress);
                CurrentExecutable = result;
                
                StatusMessage = $"Analysis complete. Found {result.FlagCount} potential flags.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Analysis failed: {ex.Message}";
            }
            finally
            {
                IsAnalyzing = false;
            }
        }

        /// <summary>
        /// Export analysis results
        /// </summary>
        private async Task ExportResultsAsync(FileExporter.ExportFormat? format)
        {
            if (CurrentExecutable == null || !format.HasValue) return;

            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "Export Analysis Results",
                    FileName = FileExporter.GetSuggestedFileName(CurrentExecutable, format.Value),
                    Filter = format.Value switch
                    {
                        FileExporter.ExportFormat.Json => "JSON Files (*.json)|*.json",
                        FileExporter.ExportFormat.Csv => "CSV Files (*.csv)|*.csv",
                        FileExporter.ExportFormat.Text => "Text Files (*.txt)|*.txt",
                        _ => "All Files (*.*)|*.*"
                    }
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    StatusMessage = "Exporting results...";
                    await FileExporter.ExportAsync(CurrentExecutable, saveFileDialog.FileName, format.Value);
                    StatusMessage = $"Results exported to: {Path.GetFileName(saveFileDialog.FileName)}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Export failed: {ex.Message}";
            }
        }

        /// <summary>
        /// Clear current results
        /// </summary>
        private void ClearResults()
        {
            CurrentExecutable = null;
            SearchText = string.Empty;
            StatusMessage = "Ready";
        }

        /// <summary>
        /// Update the filtered flags collection based on search text
        /// </summary>
        private void UpdateFilteredFlags()
        {
            FilteredFlags.Clear();

            if (CurrentExecutable?.Flags == null) return;

            var flags = CurrentExecutable.Flags.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchLower = SearchText.ToLowerInvariant();
                flags = flags.Where(f =>
                    f.ShortForm.ToLowerInvariant().Contains(searchLower) ||
                    f.LongForm.ToLowerInvariant().Contains(searchLower) ||
                    f.Description.ToLowerInvariant().Contains(searchLower) ||
                    f.ArgumentType.ToLowerInvariant().Contains(searchLower));
            }

            foreach (var flag in flags.OrderByDescending(f => f.Confidence))
            {
                FilteredFlags.Add(flag);
            }
        }

        #endregion
    }
}
