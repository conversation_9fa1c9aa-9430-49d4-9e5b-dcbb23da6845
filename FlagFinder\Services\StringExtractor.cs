using System.Text;
using System.IO;
using PeNet;
using FlagFinder.Utils;

namespace FlagFinder.Services
{
    /// <summary>
    /// Extracts strings from PE executable files
    /// </summary>
    public class StringExtractor
    {
        private readonly int _minStringLength;
        private readonly int _maxStrings;

        public StringExtractor(int minStringLength = 4, int maxStrings = Constants.MaxStringsToExtract)
        {
            _minStringLength = minStringLength;
            _maxStrings = maxStrings;
        }

        /// <summary>
        /// Extract all readable strings from a PE file
        /// </summary>
        public async Task<List<string>> ExtractStringsAsync(string filePath)
        {
            var strings = new List<string>();
            
            try
            {
                // First try using PeNet to get strings from specific sections
                var peFile = new PeFile(filePath);
                
                // Extract from .rdata section (read-only data)
                if (peFile.ImageSectionHeaders != null)
                {
                    var rdataSection = peFile.ImageSectionHeaders.FirstOrDefault(s => 
                        s.Name.Equals(".rdata", StringComparison.OrdinalIgnoreCase));
                    
                    if (rdataSection != null)
                    {
                        var sectionStrings = await ExtractStringsFromSectionAsync(filePath, rdataSection);
                        strings.AddRange(sectionStrings);
                    }
                }

                // If we don't have enough strings, do a full file scan
                if (strings.Count < 100)
                {
                    var fileStrings = await ExtractStringsFromFileAsync(filePath);
                    strings.AddRange(fileStrings);
                }

                // Remove duplicates and sort by length (longer strings first)
                return strings.Distinct()
                             .Where(s => s.Length >= _minStringLength)
                             .OrderByDescending(s => s.Length)
                             .Take(_maxStrings)
                             .ToList();
            }
            catch (Exception ex)
            {
                // Fallback to basic string extraction
                Console.WriteLine($"PE parsing failed, using fallback method: {ex.Message}");
                return await ExtractStringsFromFileAsync(filePath);
            }
        }

        /// <summary>
        /// Extract strings from a specific PE section
        /// </summary>
        private async Task<List<string>> ExtractStringsFromSectionAsync(string filePath, PeNet.Header.Pe.ImageSectionHeader section)
        {
            var strings = new List<string>();
            
            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var buffer = new byte[section.SizeOfRawData];
            
            fileStream.Seek(section.PointerToRawData, SeekOrigin.Begin);
            await fileStream.ReadAsync(buffer, 0, buffer.Length);
            
            strings.AddRange(ExtractStringsFromBytes(buffer));
            
            return strings;
        }

        /// <summary>
        /// Extract strings from entire file (fallback method)
        /// </summary>
        private async Task<List<string>> ExtractStringsFromFileAsync(string filePath)
        {
            var strings = new List<string>();
            const int bufferSize = 64 * 1024; // 64KB buffer
            
            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var buffer = new byte[bufferSize];
            int bytesRead;
            
            while ((bytesRead = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
            {
                var chunkStrings = ExtractStringsFromBytes(buffer.Take(bytesRead).ToArray());
                strings.AddRange(chunkStrings);
                
                // Limit total strings to prevent memory issues
                if (strings.Count > _maxStrings * 2)
                {
                    break;
                }
            }
            
            return strings.Distinct().Take(_maxStrings).ToList();
        }

        /// <summary>
        /// Extract printable strings from byte array
        /// </summary>
        private List<string> ExtractStringsFromBytes(byte[] bytes)
        {
            var strings = new List<string>();
            var currentString = new StringBuilder();
            
            foreach (byte b in bytes)
            {
                if (IsPrintableAscii(b))
                {
                    currentString.Append((char)b);
                }
                else
                {
                    if (currentString.Length >= _minStringLength)
                    {
                        var str = currentString.ToString().Trim();
                        if (!string.IsNullOrWhiteSpace(str) && IsLikelyCommandLineString(str))
                        {
                            strings.Add(str);
                        }
                    }
                    currentString.Clear();
                }
            }
            
            // Don't forget the last string
            if (currentString.Length >= _minStringLength)
            {
                var str = currentString.ToString().Trim();
                if (!string.IsNullOrWhiteSpace(str) && IsLikelyCommandLineString(str))
                {
                    strings.Add(str);
                }
            }
            
            return strings;
        }

        /// <summary>
        /// Check if byte is printable ASCII
        /// </summary>
        private static bool IsPrintableAscii(byte b)
        {
            return b >= 32 && b <= 126; // Printable ASCII range
        }

        /// <summary>
        /// Heuristic to determine if a string might be related to command-line usage
        /// </summary>
        private static bool IsLikelyCommandLineString(string str)
        {
            // Skip very long strings (likely not command-line related)
            if (str.Length > 200) return false;
            
            // Skip strings with too many non-alphanumeric characters
            var alphaNumCount = str.Count(char.IsLetterOrDigit);
            if (alphaNumCount < str.Length * 0.5) return false;
            
            // Look for command-line indicators
            var lowerStr = str.ToLowerInvariant();
            
            // Contains flag-like patterns
            if (str.Contains("--") || (str.Contains("-") && str.Length < 50))
                return true;
                
            // Contains help-related keywords
            if (Constants.HelpStrings.Any(keyword => lowerStr.Contains(keyword)))
                return true;
                
            // Contains usage patterns
            if (lowerStr.Contains("usage") || lowerStr.Contains("option") || lowerStr.Contains("argument"))
                return true;
                
            // Contains file/path indicators
            if (lowerStr.Contains("file") || lowerStr.Contains("path") || lowerStr.Contains("directory"))
                return true;
            
            return false;
        }
    }
}
