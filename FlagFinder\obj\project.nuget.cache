{"version": 2, "dgSpecHash": "kvYIMtDwj3g=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\flagfinder\\FlagFinder\\FlagFinder.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\csvhelper\\30.0.1\\csvhelper.30.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.toolkit.mvvm\\7.1.2\\microsoft.toolkit.mvvm.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\penet\\4.0.0\\penet.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\penet.asn1\\2.0.1\\penet.asn1.2.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\7.0.0\\system.formats.asn1.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\7.0.3\\system.security.cryptography.pkcs.7.0.3.nupkg.sha512"], "logs": []}