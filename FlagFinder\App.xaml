<Application x:Class="FlagFinder.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="clr-namespace:FlagFinder.Converters"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- Converters -->
        <converters:ConfidenceToColorConverter x:Key="ConfidenceToColorConverter" />
        <converters:BooleanToYesNoConverter x:Key="BooleanToYesNoConverter" />
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        
        <!-- Styles -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,10,0,5" />
        </Style>
        
        <Style x:Key="InfoTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Margin" Value="0,2" />
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8" />
            <Setter Property="Margin" Value="5" />
            <Setter Property="MinWidth" Value="100" />
            <Setter Property="Background" Value="#FF0078D4" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF106EBE" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF005A9E" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#FFCCCCCC" />
                                <Setter Property="Foreground" Value="#FF666666" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#FF6C757D" />
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF5A6268" />
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FF545B62" />
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="DataGridStyle" TargetType="DataGrid">
            <Setter Property="AutoGenerateColumns" Value="False" />
            <Setter Property="CanUserAddRows" Value="False" />
            <Setter Property="CanUserDeleteRows" Value="False" />
            <Setter Property="IsReadOnly" Value="True" />
            <Setter Property="SelectionMode" Value="Single" />
            <Setter Property="GridLinesVisibility" Value="Horizontal" />
            <Setter Property="HorizontalGridLinesBrush" Value="#FFE0E0E0" />
            <Setter Property="AlternatingRowBackground" Value="#FFF8F9FA" />
            <Setter Property="RowBackground" Value="White" />
            <Setter Property="HeadersVisibility" Value="Column" />
        </Style>
        
        <Style x:Key="StatusBarStyle" TargetType="StatusBar">
            <Setter Property="Background" Value="#FFF0F0F0" />
            <Setter Property="BorderBrush" Value="#FFD0D0D0" />
            <Setter Property="BorderThickness" Value="0,1,0,0" />
        </Style>
        
        <!-- Progress Bar Style -->
        <Style x:Key="ModernProgressBar" TargetType="ProgressBar">
            <Setter Property="Height" Value="4" />
            <Setter Property="Background" Value="#FFE0E0E0" />
            <Setter Property="Foreground" Value="#FF0078D4" />
            <Setter Property="BorderThickness" Value="0" />
        </Style>
    </Application.Resources>
</Application>
