using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;

namespace FlagFinder.Models
{
    /// <summary>
    /// Contains information about an analyzed executable file
    /// </summary>
    public class ExecutableInfo : INotifyPropertyChanged
    {
        private string _filePath = string.Empty;
        private string _fileName = string.Empty;
        private long _fileSize;
        private DateTime _lastModified;
        private string _architecture = string.Empty;
        private string _version = string.Empty;
        private string _description = string.Empty;
        private bool _isAnalyzed;
        private string _analysisStatus = string.Empty;
        private TimeSpan _analysisTime;
        private ObservableCollection<CommandLineFlag> _flags = new();

        /// <summary>
        /// Full path to the executable file
        /// </summary>
        public string FilePath
        {
            get => _filePath;
            set
            {
                _filePath = value;
                FileName = Path.GetFileName(value);
                OnPropertyChanged(nameof(FilePath));
            }
        }

        /// <summary>
        /// File name without path
        /// </summary>
        public string FileName
        {
            get => _fileName;
            private set
            {
                _fileName = value;
                OnPropertyChanged(nameof(FileName));
            }
        }

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize
        {
            get => _fileSize;
            set
            {
                _fileSize = value;
                OnPropertyChanged(nameof(FileSize));
                OnPropertyChanged(nameof(FileSizeFormatted));
            }
        }

        /// <summary>
        /// Formatted file size (e.g., "1.2 MB")
        /// </summary>
        public string FileSizeFormatted
        {
            get
            {
                if (FileSize < 1024) return $"{FileSize} B";
                if (FileSize < 1024 * 1024) return $"{FileSize / 1024.0:F1} KB";
                if (FileSize < 1024 * 1024 * 1024) return $"{FileSize / (1024.0 * 1024.0):F1} MB";
                return $"{FileSize / (1024.0 * 1024.0 * 1024.0):F1} GB";
            }
        }

        /// <summary>
        /// Last modified date
        /// </summary>
        public DateTime LastModified
        {
            get => _lastModified;
            set
            {
                _lastModified = value;
                OnPropertyChanged(nameof(LastModified));
            }
        }

        /// <summary>
        /// Architecture (x86, x64, etc.)
        /// </summary>
        public string Architecture
        {
            get => _architecture;
            set
            {
                _architecture = value;
                OnPropertyChanged(nameof(Architecture));
            }
        }

        /// <summary>
        /// File version if available
        /// </summary>
        public string Version
        {
            get => _version;
            set
            {
                _version = value;
                OnPropertyChanged(nameof(Version));
            }
        }

        /// <summary>
        /// File description if available
        /// </summary>
        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged(nameof(Description));
            }
        }

        /// <summary>
        /// Whether the file has been analyzed
        /// </summary>
        public bool IsAnalyzed
        {
            get => _isAnalyzed;
            set
            {
                _isAnalyzed = value;
                OnPropertyChanged(nameof(IsAnalyzed));
            }
        }

        /// <summary>
        /// Current analysis status
        /// </summary>
        public string AnalysisStatus
        {
            get => _analysisStatus;
            set
            {
                _analysisStatus = value;
                OnPropertyChanged(nameof(AnalysisStatus));
            }
        }

        /// <summary>
        /// Time taken for analysis
        /// </summary>
        public TimeSpan AnalysisTime
        {
            get => _analysisTime;
            set
            {
                _analysisTime = value;
                OnPropertyChanged(nameof(AnalysisTime));
            }
        }

        /// <summary>
        /// Collection of discovered command-line flags
        /// </summary>
        public ObservableCollection<CommandLineFlag> Flags
        {
            get => _flags;
            set
            {
                _flags = value;
                OnPropertyChanged(nameof(Flags));
                OnPropertyChanged(nameof(FlagCount));
            }
        }

        /// <summary>
        /// Number of discovered flags
        /// </summary>
        public int FlagCount => Flags?.Count ?? 0;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
